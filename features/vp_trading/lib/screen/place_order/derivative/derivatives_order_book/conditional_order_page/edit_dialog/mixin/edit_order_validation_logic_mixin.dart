import 'package:flutter/material.dart';
import 'package:vp_core/vp_core.dart';
import 'package:vp_design_system/custom_widget/app_snackbar_utils.dart';
import 'package:vp_design_system/widget/snackbar/vp_snackbar.dart';
import 'package:vp_stock_common/extension/context_extensions.dart';
import 'package:vp_trading/core/constant/constants.dart';
import 'package:vp_trading/core/extension/ext.dart';
import 'package:vp_trading/cubit/derivative/validate_condition_order/derivative_validate_condition_order_cubit.dart';
import 'package:vp_trading/generated/l10n.dart';
import 'package:vp_trading/model/enum/activation_conditions_type.dart';
import 'package:vp_trading/screen/order_container/enum/order_type_enum.dart';
import 'package:vp_trading/screen/place_order/derivative/derivatives_order_book/conditional_order_page/edit_dialog/mixin/edit_conditional_validate_mixin.dart';

mixin EditOrderValidationLogicMixin implements EditConditionalValidateMixin {
  @override
  void showErrorMessageInDialog(BuildContext context, String message) {
    if (message.isEmpty) return;
    
     context.showSnackBar(
      snackBarType: VPSnackBarType.error,
      content: message,
    );
  }

  bool validateStopOrder({
    required BuildContext context,
    required OrderTypeEnum orderAction,
    required String currentPrice,
    required String currentActivePrice,
    required ActivationConditionsType activationType,
  }) {
    if (validatePrice(context, currentPrice)) return false;

    if (validateActivePrice(context, currentActivePrice)) return false;

    final price = currentPrice.priceDerivative ?? 0;
    final activePrice = currentActivePrice.priceDerivative ?? 0;

    if (orderAction == OrderTypeEnum.buy) {
      if (price < activePrice) {
        showErrorMessageInDialog(
          context,
          VPTradingLocalize
              .current
              .derivative_active_price_must_be_less_than_price,
        );
        return false;
      }
    } else if (orderAction == OrderTypeEnum.sell) {
      if (price > activePrice) {
        showErrorMessageInDialog(
          context,
          VPTradingLocalize
              .current
              .derivative_active_price_must_be_greater_than_price,
        );
        return false;
      }
    }

    return true;
  }

  bool validateTrailingStopOrder({
    required BuildContext context,
    required String currentVolume,
    required String currentRange,
    required String currentStepPrice,
  }) {
    if (validateVolume(context, currentVolume)) return false;

    final conditionState =
        context.read<DerivativeValidateConditionOrderCubit>().state;
    if (conditionState.isErrorStepPrice) {
      context.read<DerivativeValidateConditionOrderCubit>().focusField(
        FocusKeyboard.stepPrice,
      );
      context.read<DerivativeValidateConditionOrderCubit>().onChangeStepPrice(
        currentStepPrice,
      );
      return false;
    }

    if (conditionState.isErrorRange) {
      context.read<DerivativeValidateConditionOrderCubit>().focusField(
        FocusKeyboard.range,
      );
      context.read<DerivativeValidateConditionOrderCubit>().onChangeRange(
        currentRange,
      );
      return false;
    }

    return true;
  }

  bool validateStopLossTakeProfitOrder({
    required BuildContext context,
    required OrderTypeEnum orderAction,
    required String currentPrice,
    required String currentTakeProfit,
    required String currentStopLoss,
    required String currentConditionStopLoss,
  }) {
    if (validatePrice(context, currentPrice)) return false;

    if (validateTakeProfit(context, currentTakeProfit)) return false;

    if (validateStopLoss(context, currentStopLoss)) return false;

    if (validateConditionStopLoss(context, currentConditionStopLoss)) {
      return false;
    }

    final price = currentPrice.priceDerivative ?? 0;
    final takeProfitPrice = currentTakeProfit.priceDerivative ?? 0;
    final stopLossPrice = currentStopLoss.priceDerivative ?? 0;
    final conditionStopLossPrice =
        currentConditionStopLoss.priceDerivative ?? 0;

    if (orderAction == OrderTypeEnum.buy) {
      if (takeProfitPrice <= price) {
        showErrorMessageInDialog(
          context,
          VPTradingLocalize.current.takeProfitMustBeGreaterThanPrice,
        );
        return false;
      }

      // Conditional stop loss must be less than price
      if (conditionStopLossPrice >= price) {
        showErrorMessageInDialog(
          context,
          VPTradingLocalize.current.conditionalPriceMustBeLessThanPrice,
        );
        return false;
      }

      // Stop loss cannot be greater than conditional stop loss
      if (stopLossPrice > conditionStopLossPrice) {
        showErrorMessageInDialog(
          context,
          VPTradingLocalize.current.stopLossCannotLessThanStopLossActivePrice,
        );
        return false;
      }

      // Stop loss must be less than take profit
      if (stopLossPrice >= takeProfitPrice) {
        showErrorMessageInDialog(
          context,
          VPTradingLocalize.current.stopLossMustBeLessThanTakeProfit,
        );
        return false;
      }
    } else if (orderAction == OrderTypeEnum.sell) {
      // Take profit must be less than price
      if (takeProfitPrice >= price) {
        showErrorMessageInDialog(
          context,
          VPTradingLocalize.current.takeProfitMustBeLessThanPrice,
        );
        return false;
      }

      // Conditional stop loss must be greater than price
      if (conditionStopLossPrice <= price) {
        showErrorMessageInDialog(
          context,
          VPTradingLocalize.current.conditionalPriceMustBeGreaterThanPrice,
        );
        return false;
      }

      // Stop loss cannot be less than conditional stop loss
      if (stopLossPrice < conditionStopLossPrice) {
        showErrorMessageInDialog(
          context,
          VPTradingLocalize.current.stopLossProfitCannotBeGreaterThanPrice,
        );
        return false;
      }

      // Stop loss must be greater than take profit
      if (stopLossPrice <= takeProfitPrice) {
        showErrorMessageInDialog(
          context,
          VPTradingLocalize.current.stopLossMustBeGreaterThanTakeProfit,
        );
        return false;
      }
    }

    return true;
  }
}
