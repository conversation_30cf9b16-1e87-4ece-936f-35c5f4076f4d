import 'package:flutter/cupertino.dart';

mixin EditConditionalValidateMixin {
  bool validateVolume(BuildContext context, String volume) => false;

  bool validatePrice(BuildContext context, String price) => false;

  bool validateActivePrice(BuildContext context, String price) => false;

  bool validateTakeProfit(BuildContext context, String price) => false;

  bool validateStopLoss(BuildContext context, String price) => false;

  bool validateConditionStopLoss(BuildContext context, String price) => false;

  void showErrorMessageInDialog(BuildContext context, String message);
}
